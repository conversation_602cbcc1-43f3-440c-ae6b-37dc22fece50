"""
Gmail 邮件发送器
绝对精简版本 - 只包含核心功能
"""

import base64
import json
from email.mime.text import MIMEText
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from config import Config

class GmailSender:
    def __init__(self):
        """初始化Gmail发送器"""
        self.service = None
        self._authenticate()
    
    def _authenticate(self):
        """认证Gmail API - 自动处理token"""
        try:
            Config.validate_credentials()
            creds = None
            
            # 尝试加载已保存的token
            if Config.TOKEN_FILE.exists():
                creds = Credentials.from_authorized_user_file(str(Config.TOKEN_FILE), Config.SCOPES)
            
            # 如果没有有效凭据，进行授权流程
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    creds.refresh(Request())
                else:
                    flow = InstalledAppFlow.from_client_secrets_file(
                        str(Config.CREDENTIALS_FILE), Config.SCOPES)
                    creds = flow.run_local_server(port=0)
                
                # 保存凭据供下次使用
                with open(Config.TOKEN_FILE, 'w') as token:
                    token.write(creds.to_json())
            
            # 构建Gmail服务
            self.service = build('gmail', 'v1', credentials=creds)
            
        except Exception as e:
            raise Exception(f"Gmail API认证失败: {str(e)}")
    
    def _create_message(self, to, subject, body):
        """创建邮件消息"""
        try:
            message = MIMEText(body, 'plain', 'utf-8')
            message['to'] = to
            message['subject'] = subject
            
            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode('utf-8')
            return {'raw': raw_message}
            
        except Exception as e:
            raise Exception(f"邮件消息创建失败: {str(e)}")
    
    def send_email(self, to, subject, body):
        """
        发送邮件
        
        Args:
            to (str): 收件人邮箱
            subject (str): 邮件主题
            body (str): 邮件内容（纯文本）
            
        Returns:
            dict: 发送结果 {'success': bool, 'message': str, 'message_id': str}
        """
        try:
            # 参数验证
            if not all([to, subject, body]):
                return {
                    'success': False,
                    'message': '邮件参数不完整：收件人、主题、内容都不能为空',
                    'message_id': None
                }
            
            # 创建邮件
            message = self._create_message(to, subject, body)
            
            # 发送邮件
            result = self.service.users().messages().send(
                userId='me', body=message).execute()
            
            return {
                'success': True,
                'message': f'邮件发送成功！消息ID: {result["id"]}',
                'message_id': result['id']
            }
            
        except HttpError as e:
            error_detail = json.loads(e.content.decode('utf-8'))
            return {
                'success': False,
                'message': f'Gmail API错误: {error_detail.get("error", {}).get("message", str(e))}',
                'message_id': None
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'邮件发送失败: {str(e)}',
                'message_id': None
            }
