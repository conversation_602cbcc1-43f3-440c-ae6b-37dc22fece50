"""
小樱的LLM客户端
基于systemtimeAIagent的架构
"""

import openai
from config import Config
from prompts import SAKURA_SYSTEM_PROMPT

class SakuraLLMClient:
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=Config.get_llm_api_key(),
            base_url=Config.LLM_BASE_URL
        )
        self.model = Config.LLM_MODEL
        
    def chat(self, messages, functions=None):
        """与小樱对话"""
        try:
            # 使用tools格式（新版OpenAI API）
            if functions:
                tools = [{"type": "function", "function": func} for func in functions]
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[{"role": "system", "content": SAKURA_SYSTEM_PROMPT}] + messages,
                    tools=tools,
                    tool_choice="auto",
                    temperature=0.9,  # 更高的创造性，让小樱更生动
                    max_tokens=1000
                )
            else:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[{"role": "system", "content": SAKURA_SYSTEM_PROMPT}] + messages,
                    temperature=0.9,
                    max_tokens=1000
                )
            return response
        except Exception as e:
            raise Exception(f"小樱的大脑出了点小问题呢～: {str(e)}")
    
    def chat_simple(self, user_message):
        """简单对话（无函数调用）"""
        try:
            messages = [{"role": "user", "content": user_message}]
            response = self.chat(messages)
            return response.choices[0].message.content
        except Exception as e:
            return f"呜呜～小樱有点困惑了：{str(e)}"
