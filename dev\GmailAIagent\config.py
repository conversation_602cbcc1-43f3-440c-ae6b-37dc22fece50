"""
Gmail偶像小樱的配置文件
"""

import os
from pathlib import Path

class Config:
    # LLM配置 (参考systemtimeAIagent)
    LLM_BASE_URL = "https://api.studio.nebius.ai/v1"
    LLM_MODEL = "deepseek-ai/DeepSeek-V3-0324-fast"
    
    # Gmail API配置
    GMAIL_CREDENTIALS_FILE = Path(__file__).parent / "client_secret.json"
    GMAIL_TOKEN_FILE = Path(__file__).parent / "token.json"
    GMAIL_SCOPES = ['https://www.googleapis.com/auth/gmail.modify']  # 读写权限
    
    # 偶像个性配置
    IDOL_NAME = "小樱"
    IDOL_EMAIL = None  # 将在认证后自动获取
    
    # 邮件管理配置
    MAX_EMAILS_PER_CHECK = 10  # 每次检查的最大邮件数
    EMAIL_CHECK_LABELS = ['INBOX', 'UNREAD']  # 检查的标签
    
    @classmethod
    def get_llm_api_key(cls):
        """获取LLM API密钥"""
        api_file = Path(__file__).parent.parent / "api.txt"
        with open(api_file, 'r', encoding='utf-8') as f:
            return f.read().strip()
    
    @classmethod
    def validate_gmail_credentials(cls):
        """验证Gmail凭据文件"""
        if not cls.GMAIL_CREDENTIALS_FILE.exists():
            raise FileNotFoundError(f"Gmail凭据文件不存在: {cls.GMAIL_CREDENTIALS_FILE}")
        return True
