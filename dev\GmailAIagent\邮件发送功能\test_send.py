"""
Gmail 邮件发送测试
绝对精简版本
"""

from gmail_sender import GmailSender

def test_send_email():
    """测试邮件发送功能"""
    try:
        # 初始化发送器
        print("🚀 初始化Gmail发送器...")
        sender = GmailSender()
        print("✅ Gmail API认证成功！")
        
        # 发送测试邮件
        print("\n📧 发送测试邮件...")
        
        # 请修改为你的邮箱地址
        to_email = "<EMAIL>"  # ⚠️ 请修改为实际邮箱
        subject = "Gmail API 测试邮件"
        body = """这是一封来自Gmail API的测试邮件！

如果你收到这封邮件，说明Gmail API集成成功了！

发送时间: 刚刚
发送方式: Python + Gmail API
"""
        
        result = sender.send_email(to_email, subject, body)
        
        # 显示结果
        if result['success']:
            print(f"✅ {result['message']}")
        else:
            print(f"❌ {result['message']}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def interactive_send():
    """交互式发送邮件"""
    try:
        sender = GmailSender()
        print("✅ Gmail发送器已就绪！")
        
        while True:
            print("\n" + "="*50)
            print("📧 Gmail 邮件发送器")
            print("="*50)
            
            to = input("收件人邮箱: ").strip()
            if not to:
                break
                
            subject = input("邮件主题: ").strip()
            if not subject:
                break
                
            print("邮件内容 (输入完成后按回车两次):")
            body_lines = []
            while True:
                line = input()
                if line == "":
                    break
                body_lines.append(line)
            
            body = "\n".join(body_lines)
            
            if body:
                print("\n🚀 发送中...")
                result = sender.send_email(to, subject, body)
                
                if result['success']:
                    print(f"✅ {result['message']}")
                else:
                    print(f"❌ {result['message']}")
            else:
                print("❌ 邮件内容不能为空")
                
            if input("\n继续发送？(y/n): ").lower() != 'y':
                break
                
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ 错误: {str(e)}")

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 发送预设测试邮件")
    print("2. 交互式发送邮件")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        test_send_email()
    elif choice == "2":
        interactive_send()
    else:
        print("❌ 无效选择")
