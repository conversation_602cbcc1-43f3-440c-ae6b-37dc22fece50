# 多功能MCP助手

## 功能概述

本项目在原有的文件操作MCP基础上，新增了天气查询和时间查询两个功能，形成了一个完整的多功能MCP工具集。现已统一整合到 `main.py` 中。

## 新增功能

### 1. 天气查询功能 (weather_mcp.py)
- **功能**: 查询指定城市的当前天气
- **支持城市**: 支持中文城市名（如：北京、上海、广州等）
- **返回信息**: 温度、湿度、风速、天气描述等
- **API**: 使用OpenWeatherMap API

### 2. 时间查询功能 (time_mcp.py)
- **当前时间**: 获取系统当前时间（支持多种格式）
- **时间信息**: 获取时间段、星期、季节等信息
- **格式支持**: 简单格式、详细格式、时间戳格式

## 核心文件

### 新增文件
- `weather_mcp.py` - 天气查询MCP服务器
- `time_mcp.py` - 时间查询MCP服务器
- `multi_mcp_client.py` - 多服务器MCP客户端
- `test_multi_mcp.py` - 功能测试脚本

### 修改文件
- `agent.py` - 更新为多功能MCP代理（原FileOperationAgent -> MultiMCPAgent）
- `main.py` - 更新为多功能对话界面
- `config.py` - 添加天气API配置
- `requirements.txt` - 添加requests依赖

## 使用方法

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行多功能助手
```bash
python main.py
```

### 3. 运行测试
```bash
python test_multi_mcp.py
```

## 功能演示

### 时间查询
- "现在几点了？"
- "今天是星期几？"
- "现在是什么季节？"

### 天气查询
- "北京的天气怎么样？"
- "上海今天天气如何？"
- "查询广州的天气"

### 文件操作
- "创建一个文件记录当前时间"
- "读取某个文件的内容"
- "列出目录内容"

## 技术特点

1. **模块化设计**: 每个功能独立的MCP服务器
2. **统一协议**: 使用相同的JSON-RPC协议
3. **简洁高效**: 代码简洁，无冗余功能
4. **安全限制**: 文件操作有安全边界
5. **错误处理**: 完善的异常处理机制

## 架构说明

```
main.py (主程序)
    ↓
agent.py (多功能MCP代理)
    ↓
multi_mcp_client.py (多服务器客户端)
    ↓
├── file_operations_mcp.py (文件操作服务)
├── weather_mcp.py (天气查询服务)
└── time_mcp.py (时间查询服务)
```

## 测试结果

✅ 时间查询功能正常
✅ 天气查询功能正常  
✅ 多服务器连接正常
✅ 基本文件操作正常

## 注意事项

1. 天气查询需要网络连接
2. 文件操作受安全限制，仅限指定目录
3. 所有功能都通过统一的对话界面访问
4. 支持中文交互
