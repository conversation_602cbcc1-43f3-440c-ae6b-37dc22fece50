"""
虚拟偶像小樱的核心代理
基于systemtimeAIagent的架构设计
"""

import json
from llm_client import <PERSON><PERSON><PERSON><PERSON>
from gmail_service import SakuraGmailService
from prompts import GMAIL_FUNCTIONS

class SakuraIdolAgent:
    def __init__(self):
        """初始化小樱偶像"""
        print("🌸 小樱正在准备上线...")
        self.llm = SakuraLLMClient()
        self.gmail = SakuraGmailService()
        self.conversation = []
        
        # 小樱的状态
        self.mood = "开心"  # 当前心情
        self.last_email_check = None
        
        print(f"✨ 小樱已上线！我的邮箱是: {self.gmail.user_email}")
    
    def chat(self, user_input):
        """与小樱对话"""
        self.conversation.append({"role": "user", "content": user_input})
        
        try:
            # 让小樱思考并可能调用函数
            response = self.llm.chat(self.conversation, GMAIL_FUNCTIONS)
            message = response.choices[0].message
            
            # 处理函数调用
            if message.tool_calls:
                for tool_call in message.tool_calls:
                    result = self._handle_tool_call(tool_call)
                    self.conversation.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": json.dumps(result, ensure_ascii=False)
                    })
                
                # 获取小樱的最终回应
                response = self.llm.chat(self.conversation)
                message = response.choices[0].message
            
            # 保存对话
            self.conversation.append({"role": "assistant", "content": message.content})
            
            # 更新小樱的心情（简单的情感模拟）
            self._update_mood(user_input, message.content)
            
            return message.content
            
        except Exception as e:
            return f"呜呜～小樱有点困惑了呢：{str(e)}"
    
    def _handle_tool_call(self, tool_call):
        """处理小樱的功能调用"""
        name = tool_call.function.name
        args = json.loads(tool_call.function.arguments)
        
        print(f"🌸 小樱正在{self._get_action_description(name)}...")
        
        if name == "send_email":
            to = args.get("to")
            subject = args.get("subject")
            body = args.get("body")
            return self.gmail.send_email(to, subject, body)
            
        elif name == "read_emails":
            count = args.get("count", 5)
            only_unread = args.get("only_unread", True)
            result = self.gmail.read_emails(count, only_unread)
            self.last_email_check = "刚刚"
            return result
            
        elif name == "reply_email":
            message_id = args.get("message_id")
            reply_content = args.get("reply_content")
            return self.gmail.reply_email(message_id, reply_content)
            
        elif name == "mark_email_read":
            message_id = args.get("message_id")
            return self.gmail.mark_as_read(message_id)
            
        else:
            return {"success": False, "message": f"小樱不知道怎么做{name}呢～"}
    
    def _get_action_description(self, action):
        """获取动作描述"""
        descriptions = {
            "send_email": "发送邮件",
            "read_emails": "查看邮箱",
            "reply_email": "回复邮件", 
            "mark_email_read": "整理邮件"
        }
        return descriptions.get(action, "做某件事")
    
    def _update_mood(self, user_input, response):
        """更新小樱的心情"""
        # 简单的情感分析
        if any(word in user_input.lower() for word in ['谢谢', '感谢', '棒', '好', '赞']):
            self.mood = "超开心"
        elif any(word in user_input.lower() for word in ['邮件', '发送', '查看']):
            self.mood = "认真工作中"
        elif any(word in user_input.lower() for word in ['累', '忙', '辛苦']):
            self.mood = "有点累但很充实"
        else:
            self.mood = "开心"
    
    def get_status(self):
        """获取小樱的状态"""
        return {
            "name": "小樱",
            "email": self.gmail.user_email,
            "mood": self.mood,
            "last_email_check": self.last_email_check,
            "conversation_length": len(self.conversation)
        }
    
    def reset_conversation(self):
        """重置对话"""
        self.conversation = []
        self.mood = "开心"
        return "小樱的记忆重置了呢～我们重新开始聊天吧！"
